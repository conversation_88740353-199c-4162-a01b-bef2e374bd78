# 认知测试系统产品需求文档 (PRD)

## 文档信息

| **版本** | **时间**     | **更新人** | **内容** | **位置** |
| ------ | ---------- | ------- | ------ | ------ |
| 1.0    | 2025-07-23 | 高伟      | 创建文档   | -      |

## 1. 需求

### 1.1 用户需求

- **被试用户**
  
    - **自我评估**: 希望通过系统全面、准确地评估自身的记忆力、注意力、思维能力及其他认知水平。
      
    - **心理健康洞察**: 期望了解自身的心理健康状况，包括抑郁情绪识别和精神障碍风险筛查。
      
    - **个性化建议**: 希望获得针对自身认知能力和心理健康状况的个性化训练建议和相关资源。
      
    - **操作简便**: 期望系统界面友好、操作直观，能够轻松进行测试和查看报告，尤其对于特殊人群（老年、认知障碍、视听运动功能受限等）需具备高度易用性和无障碍性。
      
    - **隐私安全**: 关注个人信息和测试数据的隐私与安全，希望系统能提供可靠的数据保护机制。
      
    - **进度追踪**: 希望能查看自己的测试历史记录，了解认知能力和情绪变化的趋势。
      
    - **情绪管理**: 希望能直观地记录和追踪情绪变化，并以温和的方式展示进展。
      
    - **指导清晰**: 期望在进行各项测试和佩戴生理传感器时，能获得清晰、多模态（文字、图片、视频、语音）的分步指导。
      
    - **非评判反馈**: 期望系统反馈（尤其是AI评估结果）是支持性、非评判性的，并能理解评估结果的影响因素。
    
- **医生工作站
  
    - **用户管理**: 能够方便地注册、管理被试账号。
      
    - **测试配置**: 能够灵活组合不同类型的测试模式，满足不同被试的需求。
      
    - **数据采集与管理**: 能够准确记录、存储和管理被试的测试时间、得分、回答、生理信号等数据。
      
    - **结果分析与报告**: 能够对被试的测试结果进行统计分析，生成详细的测试报告，并支持图表展示及数据导出（CSV）。
      
    - **录音评分**: 对于如词语流畅性测试等需要录音的测试，能够回放录音并进行手动评分和记录。
      
    - **系统监控**: 期望系统稳定、可靠。
      

### 1.2 业务需求

- **提升评估效率与准确性**: 通过系统化、标准化的测试流程，提高认知和心理健康评估的效率和数据准确性。
  
- **拓展服务范围**: 覆盖更广泛的用户群体，包括普通人群和各类特殊需求人群，提供包容服务。
  
- **数据驱动决策**: 收集和分析大量用户测试数据，为认知科学研究、临床诊断辅助和个性化干预提供数据支持。
  
- **建立行业标准与信任**: 通过专业的测试内容、可靠的技术架构和严格的隐私安全保障，建立系统在心理健康评估领域的专业性和用户信任。
  
- **实现商业价值**: 通过提供高质量的评估服务和个性化解决方案，吸引用户并实现可持续的商业运营。
  
- **模块化与可扩展性**: 系统应具备良好的模块化设计，支持按需开启和灵活的试用管理，便于未来功能扩展和迭代。
  
- **AI赋能**: 结合云计算和人工智能技术，提升评估的智能化水平，提供更深度的分析和可解释的反馈。


## 2. 设计模型

### 2.1 业务架构

系统主要分为被试端（平板/移动端）和管理端（Web端），通过后端服务和数据库进行数据交互。为了更清晰地展示各部分职责和交互，我们将业务架构图拆分为以下几个子图。
#### 2.1.1 整体系统概览

此图展示了系统主要参与者及其核心交互流。

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    subgraph 被试端 平板端
        A[用户操作]
    end

    subgraph 管理端 Web端
        F[主测操作]
    end

    subgraph 后端服务
        K[服务处理]
    end

    subgraph 基础设施
        S[数据存储]
    end

    A -- 请求/数据 --> K
    F -- 请求/数据 --> K
    K -- 读写 --> S
    K -- 返回 --> A
    K -- 返回 --> F
```

#### 2.1.2 被试端核心交互流程

此图详细描绘了被试用户如何与系统进行交互，包括注册、登录、进行测试和查看结果。

```mermaid
flowchart TB
    U[用户] -->|登录/注册| A[身份服务]
    U -->|选择测试| B[配置服务]
    U -->|提交数据| C[测试服务]
    A -->|用户数据| D[存储服务]
    B -->|测试配置| D
    C -->|测试结果| D
    D -->|生成报告| E[报告服务]
    E --> U

```

#### 2.1.3 管理端核心交互流程

此图展示了医生工作站在Web端进行管理操作，包括用户管理、测试分配、结果查看和报告生成。

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    subgraph 管理端 Web端
        F[管理后台登录] --> G[用户管理]
        G --> H{测试分配}
        G --> I[查看/评分测试结果]
        I --> J[生成/导出报告]
    end

    subgraph 后端服务
        O[身份与访问管理服务]
        K[用户管理服务]
        P[配置管理服务]
        L[认知测试服务]
        M[报告生成服务]
    end

    subgraph 基础设施
        N[数据存储服务]
        T[文件存储 录音/图片]
    end

    F -- 认证/授权 --> O
    O -- 管理权限 --> K
    G -- 管理用户/子账号 --> K
    H -- 配置测试类型 --> P
    I -- 查询/评分数据 --> L
    I -- 查看原始数据/录音 --> N
    I -- 查看原始数据/录音 --> T
    J -- 生成/导出报告 --> M
    M -- 获取数据 --> N
```

#### 2.1.4 后端服务与基础设施交互

此图聚焦于后端服务之间的内部通信以及它们与数据库和文件存储的交互。

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    subgraph 后端服务
        K[用户管理服务]
        L[认知测试服务]
        M[报告生成服务]
        O[身份与访问管理服务]
        P[配置管理服务]
        Q[试用与订阅管理服务]
        R[消息队列/事件总线]
        U[API 网关]
    end

    subgraph 基础设施
        S[数据库]
        T[文件存储 录音/图片]
    end

    被试端 -- API请求 --> U
    管理端 -- API请求 --> U
    U -- 路由/认证 --> K
    U -- 路由/认证 --> L
    U -- 路由/认证 --> M
    U -- 路由/认证 --> O
    U -- 路由/认证 --> P
    U -- 路由/认证 --> Q

    K -- 读写 --> S
    L -- 读写 --> S
    M -- 读写 --> S
    Q -- 权限更新 --> O
    Q -- 事件通知 --> R
    R -- 异步通信 --> O
    R -- 异步通信 --> K
    R -- 异步通信 --> M
    L -- 存储/读取 --> T
```

### 2.2 核心流程图

#### 2.2.1 被试用户注册与登录流程

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    A[进入平板端APP] --> B{是否已有账号?}
    B -- 否 --> C[点击“立即注册”]
    C --> D[填写注册信息 <br>手机号、姓名、学历、性别、联系电话、惯用手、是否色盲]
    D --> E[同意用户协议]
    E --> F[点击“注册”]
    F -- 注册成功 --> G[注册成功提示]
    G --> H[返回登录页面]
    B -- 是 --> I[输入账号]
    H --> I
    I --> J[点击“查询”]
    J --> K[选择测试节点]
    K --> L[同意用户协议]
    L --> M[点击“确定”]
    M -- 登录成功 --> N[进入主菜单/开始测试]
    J -- 账号错误 --> O[提示账号错误/重新输入]
    O --> I

```

#### 2.2.2 被试用户进行认知测试流程

```mermaid
graph TD  
    A[登录成功<br>进入主菜单]:::green --> B[点击“开始测试”]  
    B --> C[显示测试类别列表]:::blue  
    C --> D["选择测试项 (如PDQ-5)"]:::purple  
    D --> E[显示测试指导语]:::orange  
    E --> F[点击“开始测试”]  
    F --> G["进入练习环节 (可选)"]:::yellow  
    G -- 练习完成/跳过 --> H[正式开始测试]:::blue  
    H --> I["根据配置任务进行其他项目测试"]:::purple  
    I -- 所有测试完成 --> M[显示“谢谢配合”/返回主菜单]:::green  
    M --> N[查看测试记录和报告]:::orange  

    classDef green fill:#2ecc71,stroke:#27ae60,color:white;  
    classDef blue fill:#3498db,stroke:#2980b9,color:white;  
    classDef purple fill:#9b59b6,stroke:#8e44ad,color:white;  
    classDef orange fill:#e67e22,stroke:#d35400,color:white;  
    classDef yellow fill:#f1c40f,stroke:#f39c12,color:black;  

```

## 3. 功能列表

|                  |                                                     |            |              |
| ---------------- | --------------------------------------------------- | ---------- | ------------ |
| **模块**         | **子功能**                                          | **优先级** | **版本规划** |
| **APP首页模块**  | 被试注册 (生成患者编号)                             | P0         | V1.0         |
|                  | 患者编号或者手机号登录                              | P0         | V1.0         |
|                  | 扫码患者编号二维码登录                              | P0         | V1.0         |
|                  | 被试个人信息包括历史信息管理 (查看)                 | P0         | V1.0         |
|                  | 被试报告记录 (查看)                                 | P0         | V1.0         |
| **认知测试模块** | 测试项目列表展示                                    | P0         | V1.0         |
|                  | 测试指导语与示例                                    | P0         | V1.0         |
|                  | PDQ-5 (记忆力注意力、思维反应、认知灵活性)          | P0         | V1.0         |
|                  | Hopkins 词汇学习 (即时再认、延迟再认)               | P0         | V1.0         |
|                  | 顺背/倒背 (N-back)                                  | P0         | V1.0         |
|                  | Stroop 色词测试                                     | P0         | V1.0         |
|                  | 连线测试 (小写数字、大小写数字)                     | P0         | V1.0         |
|                  | 词语流畅性测试 (含录音功能)                         | P0         | V1.0         |
|                  | 持续性操作测试 (箭头方向判断)                       | P0         | V1.0         |
|                  | DSST (数字符号转换)                                 | P0         | V1.0         |
|                  | 掩蔽情感启动任务                                    | P0         | V1.0         |
|                  | 测试数据记录 (时间、得分、回答)                     | P0         | V1.0         |
|                  | 测试暂停/中断机制 (用户可控)                        | P1         | V1.1         |
|                  | 生理信号采集集成 (预留接口)                         | P2         | V1.2         |
| **报告生成模块** | 单项测试结果报告                                    | P0         | V1.0         |
|                  | 综合测试结果报告                                    | P0         | V1.0         |
|                  | 报告内容 (概况、结果分析、认知能力分析、建议与训练) | P0         | V1.0         |
|                  | 图表展示                                            | P0         | V1.0         |
|                  | 报告导出 (PDF/CSV)                                  | P0         | V1.0         |
|                  | 常模数据对比分析                                    | P1         | V1.1         |
|                  | AI驱动的评估结果与反馈 (可解释性XAI)                | P1         | V1.1         |
|                  | 情绪追踪与进展可视化                                | P1         | V1.1         |
| **系统管理模块** | 系统配置管理 (Web端)                                | P1         | V1.1         |
|                  | 特性开关/功能切换                                   | P1         | V1.1         |
|                  | 试用与订阅管理                                      | P1         | V1.1         |
|                  | 数据备份与恢复                                      | P1         | V1.1         |
|                  | 日志管理与监控                                      | P1         | V1.1         |
| **通用功能**     | 统一认证与授权                                      | P0         | V1.0         |
|                  | 消息通知 (系统提示、试用期提醒)                     | P1         | V1.1         |
|                  | 错误处理与提示                                      | P0         | V1.0         |
|                  | 响应式UI设计                                        | P0         | V1.0         |
|                  | 无障碍设计 (WCAG 2.1 AA级)                          | P0         | V1.0         |

_优先级：P0-核心功能，必须实现；P1-重要功能，建议实现；P2-次要功能，可后续迭代_

## 4. 功能流程

### 4.1 PDQ-5 测试流程

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    A[开始 PDQ-5 测试] --> B[显示指导语]
    B --> C[进入记忆力注意力调查]
    C --> D{逐题显示问题}
    D -- 用户选择答案 --> D
    D -- 所有问题回答完毕 --> E[进入思维反应调查]
    E --> F{逐题显示问题}
    F -- 用户选择答案 --> F
    F -- 所有问题回答完毕 --> G[进入认知灵活性调查]
    G --> H{逐题显示问题}
    H -- 用户选择答案 --> H
    H -- 所有问题回答完毕 --> I[测试结束]
    I --> J[记录所有题目选择]
    J --> K[返回测试列表/下一个测试]
```

- **异常流程**:
  
    - 用户未选择答案：系统自动跳过或提示用户选择。
      
    - 网络异常：提示用户网络连接问题，可选择重试或退出。
      

### 4.2 Hopkins 词汇学习测试流程

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    A[开始 Hopkins 词汇学习] --> B["显示指导语 (即时再认)"]
    B --> C["即时再认 - 学习阶段 (展示12张图片, 2s/张)"]
    C --> D["即时再认 - 答题阶段 (3轮, 每轮展示48张图片, 2s/3张)"]
    D -- 完成3轮 --> E[即时再认测试结束]
    E --> F[进行其他测试项目]
    F --> G["延迟再认测试 (显示指导语)"]
    G --> H["延迟再认 - 答题阶段 (选择记忆过的图片)"]
    H -- 完成答题 --> I[测试结束]
    I --> J[记录所有选择、用时、正确个数]
    J --> K[返回测试列表/下一个测试]
```

- **异常流程**:
  
    - 图片加载失败：显示错误提示，跳过当前图片或中止测试。
      
    - 用户在2秒内未选择：系统记录为无效选择，自动跳到下一题。
      
    - 网络异常：提示用户网络连接问题，可选择重试或退出。
      

### 4.3 顺背/倒背 (N-back) 测试流程

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    A[开始 N-back 测试] --> B["显示指导语 (顺背)"]
    B --> C["进行顺背数测验 (一试)"]
    C --> D["进行顺背数测验 (二试)"]
    D -- 顺背任一题目一试二试均0分 --> E[终止顺背测验]
    D -- 顺背完成 --> F["显示指导语 (倒背)"]
    E --> F
    F --> G["进行倒背数测验 (一试)"]
    G --> H["进行倒背数测验 (二试)"]
    H -- 倒背任一题目一试二试均0分 --> I[终止倒背测验]
    H -- 倒背完成 --> I
    I --> J[记录顺背位数、倒背位数、测试用时]
    J --> K[返回测试列表/下一个测试]
```

- **异常流程**:
  
    - 音频播放失败：提示用户音频设备异常，请检查。
      
    - 用户输入错误：不提供即时反馈，继续测试。
      
    - 网络异常：提示用户网络连接问题，可选择重试或退出。
      

### 4.4 Stroop 色词测试流程

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    A[开始 Stroop 色词测试] --> B[显示指导语]
    B --> C["进入练习环节 (逐一尝试5个题目直至正确)"]
    C -- 练习正确 --> D[正式开始测试]
    C -- 练习不正确 --> B[重新显示指导语/示例]
    D --> E["显示干扰刺激 (100个, 逐一向左滑动)"]
    E -- 用户选择颜色 --> E
    E -- 45秒计时结束 --> F[测试结束]
    F --> G["记录正确个数 (回答总数-错误个数)"]
    G --> H[返回测试列表/下一个测试]
```

- **异常流程**:
  
    - 图片加载失败：显示错误提示，跳过当前刺激或中止测试。
      
    - 用户选择错误：不提供即时反馈，继续测试。
      
    - 网络异常：提示用户网络连接问题，可选择重试或退出。
      

### 4.5 连线测试流程

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    A[开始连线测试] --> B["显示指导语 (小写数字连线)"]
    B --> C[触碰数字1开始]
    C --> D["连线至数字20 (不移开手指)"]
    D -- 连线顺序错误 --> E[中断连线, 从上一个正确圆圈重新开始]
    D -- 完成连线 --> F[小写数字连线完成]
    F --> G["显示指导语 (大小写数字连线)"]
    G --> H[触碰字母壹开始]
    H --> I[交替连线至所有字母和数字]
    I -- 连线顺序错误 --> E[中断连线, 从上一个正确圆圈重新开始]
    I -- 完成连线 --> J[大小写数字连线完成]
    J --> K[测试结束]
    K --> L[记录完成时间、错误次数、连续回答正确最大次数]
    L --> M[返回测试列表/下一个测试]
```

- **异常流程**:
  
    - 用户移开手指：连线中断，提示用户重新开始。
      
    - 网络异常：提示用户网络连接问题，可选择重试或退出。
      

### 4.6 词语流畅性测试流程

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    subgraph 被试端
        A[开始词语流畅性测试] --> B[显示指导语]
        B --> C[点击“开始录音”]
        C --> D["开始计时 (1分钟) & 录音"]
        D -- 1分钟结束 --> E[录音结束]
        E --> F[显示“谢谢配合”]
        F --> G[上传录音文件]
    end

    subgraph 主测端
        H[主测登录Web端] --> I[进入“词语流畅性管理”]
        I --> J[选择待评分录音]
        J --> K[播放录音]
        K --> L["主测根据录音输入动物名称 (分15s区间)"]
        L --> M["系统自动统计个数 (去重)"]
        M --> N[保存评分结果]
        N --> O[查看评分详情]
    end

    G -- 上传 --> 后端服务
    后端服务 -- 存储 --> 文件存储
    后端服务 -- 通知 --> 主测端
    N -- 存储 --> 数据库
```

- **异常流程**:
  
    - 录音失败：提示用户录音设备异常，请检查。
      
    - 录音文件上传失败：提示用户网络问题，可选择重试。
      
    - 主测未在1分钟内完成录音：系统自动结束录音。
      

### 4.7 持续性操作测试流程

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    A[开始持续性操作测试] --> B[显示指导语]
    B --> C["进入练习环节 (显示一组箭头, 尝试)"]
    C -- 练习正确 --> D[正式开始测试]
    C -- 练习错误 --> B[重新显示规则]
    D --> E["开始计时 (2分钟) & 循环显示箭头 (40次)"]
    E --> F[根据箭头方向触碰左右按钮]
    F -- 完成40次 --> G[测试结束]
    E -- 2分钟计时结束 --> G
    G --> H[记录最佳反应时间、正确次数、连续回答正确次数]
    H --> I[返回测试列表/下一个测试]
```

- **异常流程**:
  
    - 箭头图片加载失败：显示错误提示，跳过当前箭头或中止测试。
      
    - 用户在箭头出现前触碰屏幕：不记录为有效操作。
      
    - 网络异常：提示用户网络连接问题，可选择重试或退出。
      

### 4.8 DSST (数字符号转换测验) 流程

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    A[开始 DSST 测试] --> B[显示指导语]
    B --> C["进入练习环节 (5个题目, 直至正确)"]
    C -- 练习正确 --> D[正式开始测试]
    C -- 练习错误 --> B[显示正确答案/重复规则]
    D --> E["开始计时 (90秒) & 显示数字符号模板"]
    E --> F[在数字下方空格填入对应符号]
    F -- 90秒计时结束 --> G[测试结束]
    G --> H[记录正确填充个数]
    H --> I[返回测试列表/下一个测试]
```

- **异常流程**:
  
    - 模板加载失败：显示错误提示，中止测试。
      
    - 用户跳过或未按顺序填写：系统不记录为正确填充。
      
    - 网络异常：提示用户网络连接问题，可选择重试或退出。
      

### 4.9 掩蔽情感启动任务流程

```mermaid
%% 导出到 Word/PDF 仍保持可读
graph TD
    A[开始掩蔽情感启动任务] --> B[显示指导语]
    B --> C[按任意键开始练习]
    C --> D[进行2个练习event]
    D -- 练习结束 --> E[显示“即将正式开始”]
    E --> F[按 's' 正式开始]
    F --> G[循环 54 个 Events]
    subgraph 单个 Event 10秒
        H[0-1000ms: 白色固定十字] --> I[1001-1500ms: 黑屏]
        I --> J["1501-1533ms: 主要刺激图片 (A/B/C组)"]
        J --> K[1534-1566ms: 黑屏]
        K --> L[1566-2000ms: Mask 图片]
        L -- 重复 1-4 步 2 次 --> M["重复图片呈现 (2001-6000ms)"]
        M --> N["6001-10000ms: 显示问题及选择界面 (1/2/3/4)"]
        N -- 用户选择数字 --> O[记录选择及时间]
        N -- 9001ms未选择 --> P[提示“请选择”]
        O --> Q["进入黑屏 (剩余时间)"]
        Q -- 10000ms结束 --> G
    end
    G -- 54个Events完成 --> R["显示“谢谢配合” (5秒)"]
    R --> S[测试结束]
```

- **异常流程**:
  
    - 图片加载失败：显示错误提示，跳过当前event或中止测试。
      
    - 用户在规定时间内未做选择：系统记录为无效选择，并提示“请选择”。

## 5. 页面设计与页面元素

### 5.1 原型文字描述
### 5.1.1 登录页

- **登录/注册页**: 简洁的背景图（平静自然风光），居中显示登录/注册表单。登录表单包含“手机号码”输入框、“密码”输入框（或子账号输入框），“登录”按钮，以及“没有患者代码，立即注册”链接。注册表单包含“姓名”、“学历”、“性别”、“联系电话”、“测试地点”、“惯用手”、“是否色盲”等输入/选择项，以及“我已阅读并同意用户协议及隐私条款”复选框和“注册”按钮。
  
- **主菜单页**: 背景图延续平静风格。页面中央一个大圆形“开始测试”按钮，周围环绕着其他功能入口的圆形图标（如“患者信息”、“语言选择”、“条款和条件”、“被试数据收集”）。底部导航栏可能包含“用户”、“报告”、“设置”等图标。
  
- **测试类别列表页**: 顶部有“返回首页”按钮和“测试类别”菜单。主体区域以列表形式展示所有认知测试项目（如PDQ-5、Hopkins词汇学习等），每个项目旁可能有简要描述。点击进入具体测试。
  
- **测试指导页**: 顶部显示测试名称和计时器（如果适用）。主体区域大字号显示测试指导语，下方有“开始测试”按钮。指导语可配有语音播放图标。
### 5.1.2 PDQ-5
- **PDQ-5测试页**: 页面上方显示当前问题编号和问题文本，下方是5个选项（如“过去7天从来没有”、“很少”、“有时”等）。用户点击选项进行选择，自动跳转到下一题。
### 5.1.3 Hopkins
- **Hopkins词汇学习测试页**:
  
    - **记忆阶段**: 全屏显示图片，每2秒切换一张。
      
    - **再认阶段**: 屏幕中央显示3或4张图片，下方有“无”选项。用户点击选择，2秒内需完成。
### 5.1.4  顺背/倒背

- **顺背/倒背测试页**: 播放数字音频后，屏幕显示多个数字方框，用户需按顺序或倒序点击数字填入。
### 5.1.5 Stroop

- **Stroop色词测试页**: 屏幕上方显示彩色汉字（如“红”字显示为蓝色），下方显示颜色选项（红、绿、蓝）。用户需点击汉字颜色对应的选项。
### 5.1.6 连线测试
- **连线测试页**: 屏幕上随机分布数字或字母，用户需从1或“壹”开始，通过拖拽或点击连线。
### 5.1.7 词语流畅性测试
- **词语流畅性测试页**: 页面中央显示“正在录音”提示和倒计时。下方有“开始录音”按钮。管理端有录音播放条、四个15s区间输入框和总个数统计框。
### 5.1.8 持续性操作测试
- **持续性操作测试页**: 屏幕中央显示箭头（左或右），两侧有左右方向按钮。用户需快速点击对应方向按钮。

### 5.1.9 DSST测试
- **DSST测试页**: 屏幕上方显示数字与符号的对应模板，下方显示数字，用户需在数字下方空格中填入对应符号。

### 5.1.10 掩蔽情感测试
- **掩蔽情感启动任务页**: 按照Event流程显示白色十字、黑屏、刺激图片、Mask图片，最后显示问题（“表情传达的情绪是正面的还是负面的？”）和1-4数字选择。
### 5.1.11 个人档案
- **测试结果报告页**: 顶部显示患者基本信息。主体区域分为“单项测试指数”和“7项测试指数”标签页。展示各项测试的得分、用时、错误次数、连续正确次数等数据，并以折线图、柱状图等形式可视化趋势。底部有“导出报告”、“导出CSV”、“导出完整CSV”按钮。

## 6. 页面详细说明

以“PDQ-5 记忆力注意力调查”为例，说明字段/控件属性：

- **问题文本**:
  
    - **类型**: 文本
      
    - **样式**: 大字号、居中、清晰易读的无衬线字体，颜色与背景对比度高（至少4.5:1）。
      
    - **交互**: 无交互。
      
    - **输入限制**: 无。
      
    - **校验规则**: 无。
      
    - **数据来源**: 后端配置的PDQ-5问题库。
      
    - **刷新机制**: 每次加载新问题时更新。
    
- **选项按钮 (如“很少 (1-2次)”):**
  
    - **类型**: 按钮/可点击区域。
      
    - **样式**: 柔和圆角矩形，低饱和度背景色，文字居中。按钮尺寸足够大（建议至少44x44 CSS像素），间距充足。选中时有视觉反馈（如颜色变亮、边框加粗）。
      
    - **交互**: 用户点击后，记录选择，并自动跳转至下一题。
      
    - **输入限制**: 单选。
      
    - **校验规则**: 必须选择一个选项才能进入下一题（若未选择则提示）。
      
    - **数据来源**: 问题对应的预设选项。
      
    - **刷新机制**: 每次加载新问题时更新选项。
    
- **进度条**:
  
    - **类型**: 进度指示器。
      
    - **样式**: 页面底部或顶部，显示当前进度百分比或已完成题数/总题数。颜色舒缓，渐变填充。
      
    - **交互**: 无交互。
      
    - **输入限制**: 无。
      
    - **校验规则**: 无。
      
    - **数据来源**: 当前测试的已完成题目数和总题目数。
      
    - **刷新机制**: 每完成一题自动更新。
    
- **语音播放按钮 (针对指导语)**:
  
    - **类型**: 图标按钮。
      
    - **样式**: 扬声器图标，点击时有微交互反馈。
      
    - **交互**: 点击播放当前页面的指导语语音。再次点击可暂停。
      
    - **输入限制**: 无。
      
    - **校验规则**: 无。
      
    - **数据来源**: 预录制的指导语音频文件。
      
    - **刷新机制**: 页面加载时可用。
      

## 7. 全局说明

### 7.1 通用交互 / 样式规范

- **视觉美学**: 采用平静、舒缓的色彩搭配（蓝绿系或淡紫/粉色系为主），大量运用留白，减少认知负荷。支持深色模式。
  
- **字体排版**: 选择清晰易读的无衬线字体，正文字号不小于16px，行高至少1.5倍。
  
- **交互反馈**: 所有交互元素（按钮、链接、输入框）应有明确的点击反馈（微动画、颜色变化）。加载状态、成功提示、错误提示均应有清晰、温和的视觉/文字反馈。
  
- **导航一致性**: 采用用户熟悉的、可预测的导航模式（如底部导航栏、顶部返回/菜单），确保关键功能2-3次点击可达。
  
- **无障碍设计**: 遵循WCAG 2.1 AA级标准。
  
    - **键盘可访问**: 所有功能均可通过键盘操作，焦点顺序逻辑清晰，焦点指示器明显。
      
    - **对比度**: 文本与背景对比度至少4.5:1，非文本元素至少3:1。
      
    - **文本缩放**: 支持文本缩放至200%不失功能，内容可重排。
      
    - **替代文本**: 为所有非文本内容（图像、图表）提供描述性`alt`文本。
      
    - **多模态**: 重要信息和指令通过文字、图标、图示、音频旁白等多种形式呈现。
      
    - **时间控制**: 避免不必要的时间限制，或提供可调整、暂停、关闭时间限制的选项。
    
- **语言风格**: 全系统使用富有同情心、非评判性、支持性的语言。避免临床术语和负面措辞。错误提示应是建设性、引导性的。
  
- **动画**: 所有动画效果应平滑、有目的性，避免突然或惊吓用户的动画。
  
- **用户控制**: 允许用户自定义通知频率、主题、数据显示方式等。提供“暂停模式”和清晰的退出路径。
  

### 7.2 全局异常或通用逻辑

- **网络异常**: 所有涉及网络请求的操作，若发生网络异常，应有统一的错误提示（如“网络连接失败，请检查您的网络设置”），并提供重试选项。
  
- **数据保存**: 测试过程中，关键数据应支持自动保存或在用户退出前提示保存，防止数据丢失。
  
- **用户会话管理**: 用户登录后，应有会话过期机制，过期后自动登出并提示重新登录。
  
- **权限不足**: 用户尝试访问无权限的功能时，系统应给出明确的权限不足提示。
  
- **时间限制**: 对于有时间限制的测试，在时间即将结束前应有视觉或听觉提醒。超时后自动终止测试并记录结果。
  
- **数据校验**: 所有用户输入的数据（如注册信息、评分数据）应进行前端和后端双重校验，确保数据格式和内容的正确性。
  
- **图片/资源加载失败**: 若测试中图片或音频资源加载失败，应有占位符或错误提示，并尝试重新加载，不应导致整个测试中断。
  

## 8. 异构系统集成方案

系统将采用模块化架构（模块化单体），以API优先的方式进行各模块间的通信。

- **系统列表**:
  
    - **前端应用**: 被试端（平板/移动端App，Vue/React），管理端（Web管理系统，Vue/React）。
      
    - **后端服务**: 用户管理服务、认知测试服务、报告生成服务、身份与访问管理服务、配置管理服务、试用与订阅管理服务。
      
    - **数据库**: 主数据库（如MySQL/PostgreSQL）用于存储结构化数据（用户、测试结果、报告元数据），文件存储服务（如OSS/MinIO）用于存储非结构化数据（录音、图片）。
      
    - **API 网关**: 作为所有外部请求的统一入口，负责路由、认证、授权、限流。
      
    - **消息队列/事件总线**: 用于模块间的异步通信和事件通知（如试用期到期事件）。
      
    - **AI/ML服务**: 用于抑郁情绪识别、精神障碍风险筛查、认知任务分析、XAI解释等。
      
    - **生理信号采集设备**: 通过蓝牙或USB连接，提供SDK或API与系统集成。
    
- **接口协议**:
  
    - **内部服务通信**: 统一采用 RESTful API，数据格式为 JSON。使用 OpenAPI/Swagger 定义API契约。
      
    - **外部设备集成**: 生理信号采集设备通过其提供的SDK或标准蓝牙/USB协议进行数据传输。
      
    - **文件传输**: 录音、图片等大文件通过预签名URL上传至文件存储服务。
    
- **数据映射**:
  
    - **用户数据**: 被试注册信息、主测信息、子账号信息等，统一映射到用户管理服务的数据模型。
      
    - **测试数据**: 各项认知测试的原始回答、用时、得分等，统一映射到认知测试服务的数据模型，并存储在数据库中。
      
    - **报告数据**: 原始测试数据经过报告生成服务处理后，生成报告所需的数据结构。
      
    - **生理信号数据**: 采集到的脉搏、心电、脑电等数据，映射为统一的生理数据模型，存储并供AI服务分析。
    
- **错误处理策略**:
  
    - **统一错误码**: 定义全局统一的错误码和错误信息，便于前后端及各服务间快速定位问题。
      
    - **异常捕获**: 各服务层应有完善的异常捕获机制，将业务异常转换为统一的错误响应。
      
    - **日志记录**: 关键接口调用、数据处理、异常发生时，应详细记录日志，便于问题追踪和排查。
      
    - **重试机制**: 对于偶发性网络或服务错误，客户端和部分后端服务应实现合理的重试机制。
      
    - **熔断/降级**: 在微服务架构下，引入熔断和降级机制，防止单个服务故障导致整个系统雪崩。
      

## 9. 非功能需求

### 9.1 性能

- **响应时间**:
  
    - 用户登录/注册：峰值负载下，响应时间 ≤ 1秒。
      
    - 测试开始/切换题目：响应时间 ≤ 500毫秒。
      
    - 测试数据提交：响应时间 ≤ 1秒。
      
    - 报告生成/导出：复杂报告生成时间 ≤ 5秒，简单报告 ≤ 2秒。
    
- **并发量**:
  
    - 支持至少 1000 并发用户同时在线进行测试。
      
    - 支持至少 100 并发用户同时进行报告查询/导出。
    
- **吞吐量**:
  
    - 系统每秒处理的请求数 (TPS) 达到 500。
    
- **数据存储**:
  
    - 支持千万级用户数据和亿级测试记录的存储与查询。
      

### 9.2 兼容性

- **设备兼容**:
  
    - 平板端App：兼容主流 Android 和 iOS 平板设备（近3年发布型号）。
      
    - Web端：兼容主流浏览器（Chrome, Firefox, Safari, Edge 最新两个大版本），支持不同分辨率的桌面和移动设备。
    
- **操作系统兼容**:
  
    - App兼容 Android 10+ 和 iOS 14+。
      
    - Web端兼容 Windows, macOS, Linux 等主流操作系统。
    
- **网络环境兼容**:
  
    - 支持在 Wi-Fi 和蜂窝网络 (4G/5G) 环境下稳定运行。
      
### 9.3 安全

- **用户认证与授权**:
  
    - 采用强密码策略，支持手机号验证码登录。
      
    - 所有用户凭证加密存储。
      
    - 基于角色的访问控制 (RBAC)，确保用户只能访问其权限范围内的功能和数据。
      
    - API接口进行严格的认证和授权校验。
    
- **数据传输安全**:
  
    - 所有数据传输采用 HTTPS/SSL 加密。
      
    - 敏感数据（如测试结果、个人身份信息）在传输和存储过程中进行加密。
    
- **防攻击**:
  
    - 防范常见的网络攻击（如SQL注入、XSS、CSRF、DDoS）。
      
    - 定期进行安全漏洞扫描和渗透测试。
    
- **代码安全**:
  
    - 开发过程中遵循安全编码规范，避免安全漏洞。
      

### 9.4 隐私

- **数据最小化**:
  
    - 仅收集业务所需的最少用户数据。
    
- **隐私政策**:
  
    - 明确的用户隐私政策，告知用户数据收集、使用、存储和保护方式。
      
    - 用户注册时需明确同意隐私政策。
    
- **数据访问控制**:
  
    - 严格控制用户数据的访问权限，只有授权人员才能访问，并记录访问日志。
      
    - 敏感数据进行脱敏处理。
    
- **用户权利**:
  
    - 提供用户数据查询、修改、导出和删除的途径。
    
- **合规性**:
  
    - 遵循相关数据隐私法规（如GDPR、国内数据安全法等）。